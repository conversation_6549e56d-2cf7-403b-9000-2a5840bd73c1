import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ILike, In, Repository } from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { IssueCoupon } from './issue-coupon.entity';
import { CrudRequest } from 'src/core/crud/crud';
import { CreateIssueCouponDto } from './dto/createIssueCoupon.dto';
import { CouponItem } from './coupon-item.entity';
import { SendManyCouponCodeDto } from './dto/sendManyCouponCode.dto';
import { waMessage } from 'src/core/exception/exception.messages.contants';
import { MailerService } from '../mailer/mailer.service';
import handlebars from 'handlebars';
import { couponCodeTemplate } from '../mailer/templates';
import { SendOneCouponCodeDto } from './dto/sendOneCouponCode.dto';
import { PeriodUnit } from 'src/core/enums/entity';
import * as moment from 'moment';
import { CouponItemListQueryDto } from './dto/issueCoupon.dto';
import {
  countExpiryDate,
  getCustomPaginationLimit,
} from '../../core/common/common.utils';
import { renderMJML } from '../mailer/templates/mjml';
import { SEND_COUPON_CODE } from '../mailer/templates/mjml/constant';
import { Setting } from '../setting/setting.entity';

@Injectable()
export class IssueCouponService extends BaseCrudService<IssueCoupon> {
  constructor(
    @InjectRepository(IssueCoupon) repo: Repository<IssueCoupon>,
    @InjectRepository(CouponItem)
    private couponItemRepo: Repository<CouponItem>,
    private readonly mailerService: MailerService,
  ) {
    super(repo);
  }

  async getIssueCoupon(name: string, keySearch?: string, branchIds?: string[]) {
    const whereClause: Record<string, any> = {};
    if (name !== undefined) {
      whereClause.coupon = {};
      whereClause.coupon.name = ILike(`%${name}%`);
    }

    if (branchIds?.length > 0) {
      whereClause.branches = {};
      whereClause.branches.id = In(branchIds);
    }
    if (keySearch) {
      whereClause.coupon = {};
      whereClause.coupon.name = ILike(`%${keySearch}%`);
    }

    const issueCouponList = await this.repo.find({
      where: whereClause,
      relations: ['coupon'],
    });

    let issueCouponListRes = [];
    if (issueCouponList.length > 0) {
      // Use transaction to ensure consistency
      await this.repo.manager.transaction(async (manager) => {
        for (const issueCoupon of issueCouponList) {
          const couponItemList = await this.getCouponItemList(issueCoupon.id);

          if (couponItemList.length > 0) {
            const countUsed = couponItemList.filter(
              (item) => !!item.email && item.isUsed === true,
            ).length;
            const countSold = couponItemList.filter(
              (item) => !!item.email,
            ).length;
            const remain = issueCoupon.issued - countSold;

            // If all coupons are sold, set status to inactive
            if (remain === 0) {
              const inactiveStatus = await manager.findOne(Setting, {
                where: { name: 'Inactive', type: 'status' },
              });

              if (inactiveStatus) {
                await manager.update(IssueCoupon, issueCoupon.id, {
                  status: inactiveStatus,
                  used: countUsed,
                  remain: remain,
                });
              }
            } else {
              await manager.update(IssueCoupon, issueCoupon.id, {
                used: countUsed,
                remain: remain,
              });
            }
          }
        }
      });

      // Fetch fresh data after updates
      const issueCouponArr = await this.repo.find({
        where: whereClause,
        relations: ['status', 'coupon', 'branches'],
      });

      issueCouponListRes = issueCouponArr.map(function (iCoupon) {
        return {
          ...iCoupon,
        };
      });

      issueCouponListRes = issueCouponListRes.sort((a, b) => {
        const dateA: Date = new Date(a.issueDate);
        const dateB: Date = new Date(b.issueDate);
        return dateB.getTime() - dateA.getTime();
      });
    }
    return issueCouponListRes;
  }

  async createOneIssueCoupon(
    crudRequest: CrudRequest,
    dto: CreateIssueCouponDto,
  ): Promise<IssueCoupon> {
    const MAX_ISSUED_LIMIT = 100000;
    if (dto.issued > MAX_ISSUED_LIMIT) {
      throw new Error(
        `The issued value (${dto.issued}) exceeds the maximum allowed limit of ${MAX_ISSUED_LIMIT}.`,
      );
    }

    dto.remain = 0;
    dto.used = 0;

    const issueCoupon = await super.createOne(crudRequest, dto);

    if (issueCoupon?.issued > 0) {
      await this.repo.manager.transaction(async (manager) => {
        const codes = await Promise.all(
          Array.from({ length: issueCoupon.issued }).map(async (_, index) => {
            const startNo = String(Number(issueCoupon?.startNo) + index);
            return this.gennerateCode({
              startNo,
              prefix: issueCoupon?.prefix || '',
              suffix: issueCoupon?.suffix || '',
              minLength: issueCoupon?.minLength,
            });
          }),
        );

        const couponItems = codes.map((code) => ({
          code,
          issueCoupon: { id: issueCoupon.id },
        }));

        await manager.save(CouponItem, couponItems);
      });
    }

    return issueCoupon;
  }

  async UpdateIssueCoupon(
    crudRequest: CrudRequest,
    dto: CreateIssueCouponDto,
  ): Promise<IssueCoupon> {
    const MAX_ISSUED_LIMIT = 100000;
    if (dto.issued > MAX_ISSUED_LIMIT) {
      throw new Error(
        `The issued value (${dto.issued}) exceeds the maximum allowed limit of ${MAX_ISSUED_LIMIT}.`,
      );
    }

    delete dto.remain;
    delete dto.used;
    const oldIssueCoupon = await super.getOne(crudRequest);
    const getLatestCouponCode = await this.couponItemRepo.findOne({
      where: {
        issueCoupon: { id: oldIssueCoupon.id },
      },
      order: {
        code: 'DESC',
      },
    });
    const latestCouponCode = getLatestCouponCode?.code;
    const remainingCharacters = latestCouponCode?.substring(6);

    if (oldIssueCoupon.issued < dto.issued) {
      await this.repo.manager.transaction(async (manager) => {
        const createQuantity = dto.issued - oldIssueCoupon.issued;
        for (let index = 1; index <= createQuantity; index++) {
          const generateCode = await this.gennerateCode({
            startNo: String(
              Number(oldIssueCoupon?.startNo) +
                Number(oldIssueCoupon.issued) +
                index -
                1,
            ),
            prefix: oldIssueCoupon?.prefix || '',
            suffix: oldIssueCoupon?.suffix || '',
            minLength: oldIssueCoupon?.minLength,
          });

          await manager.save(CouponItem, {
            code: generateCode,
            issueCoupon: { id: oldIssueCoupon.id },
          });
        }
        await manager.save(IssueCoupon, {
          id: oldIssueCoupon.id,
          remain: oldIssueCoupon.remain + (dto.issued - oldIssueCoupon.issued),
        });
      });
    } else if (oldIssueCoupon.issued > dto.issued) {
      const deleteQuantity = oldIssueCoupon.issued - dto.issued;

      const availableCouponCode = await this.couponItemRepo
        .createQueryBuilder('couponItem')
        .andWhere('couponItem.isUsed = :isUsed', { isUsed: false })
        .andWhere('couponItem.email IS NULL')
        .andWhere('couponItem.issueCoupon.id = :issueCouponId', {
          issueCouponId: oldIssueCoupon.id,
        })
        .getCount();

      if (deleteQuantity > availableCouponCode) {
        throw new HttpException(
          [
            {
              field: 'issued',
              constraints: waMessage.exception.invalidIssueCoupon.message,
            },
          ],
          HttpStatus.BAD_REQUEST,
        );
      }

      let countStopFor = 0;
      await this.repo.manager.transaction(async (manager) => {
        for (let index = 1; index <= deleteQuantity; index++) {
          if (countStopFor >= deleteQuantity) {
            break;
          }
          const checkCouponCode = await this.couponItemRepo
            .createQueryBuilder('couponItem')
            .andWhere('couponItem.isUsed = :isUsed', { isUsed: false })
            .andWhere('couponItem.email IS NULL')
            .andWhere('couponItem.deleted IS NULL')
            .andWhere('couponItem.issueCoupon.id = :issueCouponId', {
              issueCouponId: oldIssueCoupon.id,
            })
            .orderBy('code', 'DESC')
            .getOne();

          if (checkCouponCode) {
            await this.couponItemRepo.delete({
              id: checkCouponCode.id,
            });
            countStopFor++;
          }
        }
        await manager.save(IssueCoupon, {
          id: oldIssueCoupon.id,
          remain: oldIssueCoupon.remain - deleteQuantity,
        });
      });
    }
    await super.updateOne(crudRequest, dto);
    return await super.getOne(crudRequest);
  }

  async sendManyCouponCode(dto: SendManyCouponCodeDto, res) {
    if (dto.emails.length > 0 && dto.issueCouponId) {
      const availableCouponList = await this.getCouponItemList(
        dto.issueCouponId,
        true,
      );

      const contentTemplate = handlebars.compile(couponCodeTemplate);
      if (availableCouponList.length >= dto.emails.length) {
        for (let index = 0; index < dto.emails.length; index++) {
          await this.isEmailValid(dto.emails[index]);
          const html = contentTemplate({
            couponCode: availableCouponList[index].code,
          });
          this.mailerService.sendMail({
            to: dto.emails[index],
            html,
            subject: 'Coupon Code',
            text: 'Coupon Code',
          });

          await this.repo.manager.transaction(async (manager) => {
            await manager.save(CouponItem, {
              id: availableCouponList[index].id,
              email: dto.emails[index],
            });
          });
        }
      } else {
        throw new BadRequestException(
          waMessage.exception.insufficientCoupon.message,
        );
      }

      res.send({
        statusCode: 200,
        message: 'Email sent successfully',
      });
    }
  }

  async sendOneCouponCode(dto: SendOneCouponCodeDto, res) {
    if (dto.email && dto.couponCode) {
      // await this.isEmailValid(dto.email);
      const couponCode = await this.couponItemRepo.findOne({
        where: {
          code: dto.couponCode,
          isUsed: false,
        },
        relations: ['issueCoupon', 'issueCoupon.coupon'],
      });

      if (!couponCode) {
        throw new BadRequestException(
          waMessage.exception.invalidCouponCode.message,
        );
      }

      const templateHTML = await renderMJML(SEND_COUPON_CODE);
      const contentTemplate = handlebars.compile(templateHTML.html);
      const contentHTML = [];

      const expiredDate = couponCode.expiryDate;
      const dayFormat = String(expiredDate.getDate()).padStart(2, '0');
      const monthFormat = String(expiredDate.getMonth() + 1).padStart(2, '0');
      const yearFormat = expiredDate.getFullYear();

      contentHTML.push({
        name: couponCode.issueCoupon.coupon.name,
        code: dto.couponCode,
        expiredDate: `${dayFormat}/${monthFormat}/${yearFormat}`,
        isFirst: true,
        isRectangle: false,
        length: 1,
      });

      const html = contentTemplate({
        coupons: contentHTML,
      });

      this.mailerService.sendMail({
        to: dto.email,
        html,
        subject: 'Coupon Code',
        text: 'Coupon Code',
      });

      await this.repo.manager.transaction(async (manager) => {
        await manager.save(CouponItem, {
          id: couponCode.id,
          email: dto.email,
        });
      });

      res.send({
        statusCode: 200,
        message: 'Email sent successfully',
      });
    }
  }

  async isEmailValid(email: string): Promise<boolean> {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new BadRequestException(waMessage.exception.invalidEmail.message);
    }
    return true;
  }

  async getCouponItemList(
    issueCouponId: string,
    useful?: boolean,
    query?: CouponItemListQueryDto,
  ): Promise<any> {
    let qLimit;
    let offset;
    if (query) {
      qLimit = getCustomPaginationLimit(query.limit);
      offset = (query.page - 1) * query.limit || 0;
    }
    const queryBuilder = this.couponItemRepo
      .createQueryBuilder('couponItem')
      .leftJoinAndSelect('couponItem.status', 'status')
      .where('couponItem.deleted IS NULL');

    if (issueCouponId) {
      queryBuilder.andWhere('couponItem.issueCoupon.id = :issueCouponId', {
        issueCouponId,
      });
    }

    if (useful) {
      queryBuilder.andWhere(
        'couponItem.email IS NULL AND couponItem.isUsed = :isUsed',
        { isUsed: false },
      );
    }

    if (query?.keySearch) {
      queryBuilder.andWhere('couponItem.code ILIKE :keySearch', {
        keySearch: `%${query.keySearch}%`,
      });
    }

    if (qLimit) {
      queryBuilder.take(qLimit);
      queryBuilder.skip(offset);
      const [data, total] = await queryBuilder
        .orderBy('couponItem.code', 'ASC')
        .getManyAndCount();
      return this.createPageInfo<any>(
        data,
        total,
        qLimit || total,
        offset || 0,
      );
    } else {
      return queryBuilder.orderBy('couponItem.code', 'ASC').getMany();
    }
  }

  async formatNumberToFourDigits(number) {
    let numberString = number.toString();
    const leadingZeros = Math.max(4 - numberString.length, 0);
    numberString = '0'.repeat(leadingZeros) + numberString;
    return numberString;
  }

  async randomCode(characters: string, length: number) {
    characters = characters.replace(/[^a-zA-Z]/g, ''); // Loại bỏ tất cả các ký tự số, khoảng trắng và ký tự đặc biệt
    let result = '';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }

  async gennerateCode({
    startNo,
    prefix,
    suffix,
    minLength,
  }: {
    startNo: string;
    prefix?: string;
    suffix?: string;
    minLength?: number;
  }) {
    const result = prefix + `${startNo.padStart(minLength, '0')}` + suffix;

    return result;
  }

  async getCouponItemListByEmail(email: string, isCombo = false): Promise<any> {
    const queryBuilder = this.couponItemRepo
      .createQueryBuilder('couponItem')
      .where('couponItem.email = :email', { email: email })
      .andWhere('couponItem.isUsed = false')
      .andWhere(
        '(couponItem.expiryDate IS NULL OR couponItem.expiryDate > :date)',
        {
          date: moment().startOf('day').toDate(),
        },
      )
      .innerJoin('couponItem.issueCoupon', 'issueCoupon')
      .innerJoin('issueCoupon.coupon', 'coupon')
      .leftJoin('coupon.avatar', 'avatar')
      .leftJoinAndSelect('coupon.products', 'products')
      .groupBy('coupon.id')
      .addGroupBy('avatar')
      .addGroupBy('couponItem.issueCoupon')
      .addGroupBy('issueCoupon.id')
      .addGroupBy('coupon.name')
      .addGroupBy('coupon.price')
      .addGroupBy('coupon.period')
      .addGroupBy('coupon.periodUnit')
      .addGroupBy('coupon.discountValue')
      .addGroupBy('coupon.discountType')
      .addGroupBy('coupon.category')
      .addGroupBy('coupon.status')
      .addGroupBy('coupon.created')
      .addGroupBy('coupon.updated')
      .addGroupBy('coupon.deleted')
      .addGroupBy('coupon.avatar');

    queryBuilder.select([
      'coupon.id AS id',
      'coupon.name AS couponName',
      'avatar.url AS avatar',
      'coupon.price AS price',
      'COUNT(DISTINCT couponItem.id) AS count',
      'ARRAY_AGG(DISTINCT couponItem.code) AS codes',
      'MIN(couponItem.expiryDate) AS expiredDate',
      'ARRAY_AGG(DISTINCT products.id) AS productids',
      'ARRAY_AGG(DISTINCT products.name) AS productnames',
    ]);

    const listCouponItems = await queryBuilder.getRawMany();

    const grouped = {};
    for (const item of listCouponItems) {
      const id = item.id;
      if (!grouped[id]) {
        grouped[id] = {
          ...item,
          productids: [],
          productnames: [],
          products: [],
        };
      }
      if (item.productids && Array.isArray(item.productids)) {
        item.productids.forEach((pid, idx) => {
          if (pid) {
            grouped[id].productids.push(pid);
            grouped[id].productnames.push(
              item.productnames ? item.productnames[idx] : null,
            );
            grouped[id].products.push({
              id: pid,
              name: item.productnames ? item.productnames[idx] : null,
            });
          }
        });
      } else if (item.productids && typeof item.productids === 'string') {
        const ids = item.productids.replace(/[{}]/g, '').split(',');
        const names = item.productnames
          ? item.productnames.replace(/[{}]/g, '').split(',')
          : [];
        ids.forEach((pid, idx) => {
          if (pid) {
            grouped[id].productids.push(pid);
            grouped[id].productnames.push(names[idx] || null);
            grouped[id].products.push({
              id: pid,
              name: names[idx] || null,
            });
          }
        });
      }
    }
    let result = Object.values(grouped).map((item: any) => ({
      ...item,
      count: parseInt(item.count, 10),
      codes: Array.isArray(item.codes)
        ? item.codes
        : item.codes
        ? item.codes.replace(/[{}]/g, '').split(',')
        : [],
      products: item.products,
    }));
    if (isCombo) {
      result = result.filter((item) => item.products.length > 1);
    } else {
      result = result.filter((item) => item.products.length === 1);
    }
    return result;
  }

  async getCouponItemByEmailAndCode(code: string): Promise<any> {
    const queryBuilder = this.couponItemRepo
      .createQueryBuilder('couponItem')
      .innerJoin('couponItem.issueCoupon', 'issueCoupon')
      .innerJoin('issueCoupon.coupon', 'coupon')
      .leftJoin('coupon.avatar', 'avatar')
      .leftJoinAndSelect('coupon.products', 'products')
      .where('couponItem.code = :code', { code })
      .andWhere('couponItem.isUsed = false')
      .andWhere('"couponItem"."expiryDate" > :date', {
        date: moment().startOf('day').toDate(),
      })
      .groupBy('coupon.id')
      .addGroupBy('"expiredDate"')
      .addGroupBy('avatar')
      .select('coupon.id', 'id')
      .addSelect(
        `TO_CHAR("couponItem"."expiryDate" AT TIME ZONE 'Asia/Bangkok', 'DD/MM/YYYY') AS "expiredDate"`,
      )
      .addSelect('coupon.name', 'couponName')
      .addSelect('avatar.url', 'avatar')
      .addSelect('coupon.price', 'price')
      .addSelect('coupon.price', 'price')
      .addSelect('COUNT(DISTINCT couponItem.id)', 'count')
      .addSelect('ARRAY_AGG(DISTINCT couponItem.code)', 'codes');

    const couponItem = await queryBuilder.getRawOne();

    return couponItem;
  }

  async deleteOneIssueCoupon(issueCouponId: string) {
    return await this.repo.softDelete(issueCouponId);
  }
}
