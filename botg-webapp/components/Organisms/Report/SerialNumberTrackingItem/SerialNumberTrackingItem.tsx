'use client';

import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { useSession } from 'next-auth/react';
import { Fragment, useState, useRef } from 'react';
import { useReactToPrint } from 'react-to-print';
import { Box } from '@mui/material';
import CustomTableRow from '@/components/Atoms/CustomTableRow';
import { TColumnTableReport } from '@/components/Atoms/ReportTable';
import { StyledBodyTableCell } from '@/components/Atoms/ReportTable/ReportTable.styled';
import { Typography } from '@/components/Atoms/Typography/Typography';
import Price from '@/components/Molecules/Price';
import { ReportFilter } from '@/components/Molecules/ReportFilterSerialNumber/ReportFilter';
import ReportHeader from '@/components/Molecules/ReportHeader';
import ServiceReportTable from '@/components/Molecules/ServiceReportTable';
import { FORMAT_TIME_FULL } from '@/lib/constants/dateTime';
import { SWRKey } from '@/lib/constants/SWRKey';
import { useReport } from '@/lib/hooks/queries/report';
import { TSerialNumberTrackingItemReport } from '@/lib/types/entities/report';
import { ERangeDate } from '@/lib/types/enum/report';
import { toQueryString } from '@/lib/utils/string';
import { customTypography } from '@/theme/customTypography';
import { StyledContainer } from '../Report.styled';

dayjs.extend(utc);
dayjs.extend(timezone);

type TActions = 'edit' | 'delete' | 'create' | 'edit';

type TTableActions = 'edit' | 'delete';

type TFormValues = {
  keySearch?: string;
  purchasedStartDate?: string;
  purchasedEndDate?: string;
  redeemedStartDate?: string;
  redeemedEndDate?: string;
};

type TReturnItemProps = {
  canExport?: boolean;
};

export const SerialNumberTrackingItem: React.FC<TReturnItemProps> = ({ canExport = false }) => {
  const [filterValues, setFilterValues] = useState<TFormValues>({
    purchasedStartDate: dayjs().startOf('month').format(FORMAT_TIME_FULL),
    purchasedEndDate: dayjs().endOf('month').format(FORMAT_TIME_FULL),
    redeemedStartDate: dayjs().startOf('month').format(FORMAT_TIME_FULL),
    redeemedEndDate: dayjs().endOf('month').format(FORMAT_TIME_FULL),
  });
  const [pagination, setPagination] = useState(1);
  const componentToPrintRef = useRef<HTMLDivElement>();
  const handlePrint = useReactToPrint({
    content: () => componentToPrintRef?.current || null,
  });
  const { data: SerialNumberTrackingItemReport } = useReport<TSerialNumberTrackingItemReport, TFormValues>(
    'report-serial-number-tracking',
    filterValues,
    {
      page: pagination,
      limit: SWRKey.REPORT.LIMIT,
    }
  );
  const { data: session } = useSession();
  const currencyCode = session?.user?.branchChosen?.currency?.code
    ? ` (${session?.user?.branchChosen?.currency?.code})`
    : '';
  const maxPage = SerialNumberTrackingItemReport?.pageCount || 1;
  const COLUMNS: TColumnTableReport<TSerialNumberTrackingItemReport>[] = [
    {
      name: 'coupon',
      label: 'Coupon',
      tableCellProps: { align: 'left' },
      render: (row: TSerialNumberTrackingItemReport) => (
        <Typography variant="body-medium-400">{row?.coupon}</Typography>
      ),
    },
    {
      name: 'serialNumber',
      label: 'Serial number',
      render: (row: TSerialNumberTrackingItemReport) => (
        <Typography variant="body-medium-400">{row?.serialNumber}</Typography>
      ),
    },
    {
      name: 'soldDate',
      label: 'SOLD',
      tableCellProps: { align: 'center' },
      children: [
        {
          name: 'soldDate',
          label: 'Date',
          tableCellProps: {
            align: 'left',
          },
          render: (row: TSerialNumberTrackingItemReport) => (
            <Typography variant="body-medium-400">{row?.soldDate}</Typography>
          ),
        },
        {
          name: 'soldCustomer',
          label: 'Customer',
          render: (row: TSerialNumberTrackingItemReport) => (
            <Typography variant="body-medium-400">{row?.soldCustomer}</Typography>
          ),
        },
        {
          name: 'soldReferenceCode',
          label: 'Reference code',
          tableCellProps: { align: 'left' },
          render: (row: TSerialNumberTrackingItemReport) => (
            <Typography variant="body-medium-400">{row?.soldReferenceCode}</Typography>
          ),
        },
      ],
    },
    {
      name: 'usedDate',
      label: 'USED',
      tableCellProps: { align: 'center' },
      children: [
        {
          name: 'usedDate',
          label: 'Date',
          render: (row: TSerialNumberTrackingItemReport) => (
            <Typography variant="body-medium-400">{row?.usedDate}</Typography>
          ),
        },
        {
          name: 'usedCustomer',
          label: 'Customer',
          tableCellProps: { align: 'left' },
          render: (row: TSerialNumberTrackingItemReport) => (
            <Typography variant="body-medium-400">{row?.usedCustomer}</Typography>
          ),
        },
        {
          name: 'usedReferenceCode',
          label: 'Reference code',
          tableCellProps: { align: 'left' },
          render: (row: TSerialNumberTrackingItemReport) => (
            <Typography variant="body-medium-400">{row?.usedReferenceCode}</Typography>
          ),
        },
      ],
    },
  ];

  return (
    <StyledContainer>
      <ReportFilter<TFormValues>
        onFilter={(value: TFormValues) => {
          setPagination(1);
          setFilterValues(value);
        }}
        rangeDateLabel="Purchase from"
        rangeDateLabel2="Redeemed from"
        fieldOptionNames={['keySearch', 'rangeDate', 'rangeDate2']}
        useFormProps={{
          defaultValues: {
            purchasedStartDate: dayjs().startOf('month').format(FORMAT_TIME_FULL),
            purchasedEndDate: dayjs().endOf('month').format(FORMAT_TIME_FULL),
            redeemedStartDate: dayjs().startOf('month').format(FORMAT_TIME_FULL),
            redeemedEndDate: dayjs().endOf('month').format(FORMAT_TIME_FULL),
            rangeDateType: ERangeDate['SELECT_DATE'],
          } as unknown as TFormValues,
        }}
      />
      <ReportHeader
        title="SERIAL NUMBER TRACKING"
        url={`${SWRKey.REPORT.exportByName(
          'report-serial-number-tracking'
        )}&fileName=serial-number-tracking-report.csv&${toQueryString({
          ...filterValues,
        })}`}
        canExport={canExport}
        // canPrint
        // handlePrint={handlePrint}
      />

      <Box ref={componentToPrintRef}>
        <ServiceReportTable<TSerialNumberTrackingItemReport>
          page={pagination}
          onLoadMore={() => {
            setPagination(pre => {
              const newPage = pre + 1;
              if (newPage > maxPage) return pre;
              return newPage;
            });
          }}
          rows={SerialNumberTrackingItemReport?.data || []}
          columns={COLUMNS}
          tableFooter={
            <Fragment key="table-rows-container-total">
              <CustomTableRow key="table-rows-total">
                <StyledBodyTableCell
                  key="text-total"
                  component="th"
                  scope="row"
                  colSpan={3}
                  sx={{
                    '&.MuiTableCell-root': {
                      tableLayout: 'auto',
                      borderBottom: 'none',
                      textAlign: 'right',
                      padding: '8px',
                    },
                    '& > h5': {
                      fontSize: customTypography['heading-medium-700'],
                      textTransform: 'uppercase',
                    },
                  }}
                >
                  <Typography variant="heading-xsmall-700" textTransform="uppercase">
                    Grand total:
                  </Typography>
                </StyledBodyTableCell>
                <StyledBodyTableCell
                  key="total"
                  component="th"
                  scope="row"
                  $hasborder
                  colSpan={3}
                  sx={{
                    '&.MuiTableCell-root': {
                      tableLayout: 'auto',
                      borderBottom: 'none',
                      textAlign: 'center',
                      padding: '8px',
                    },
                  }}
                >
                  <Price
                    typographyProps={{ variant: 'heading-xsmall-700', textTransform: 'uppercase' }}
                    showSymbol={false}
                    amount={Number(SerialNumberTrackingItemReport?.totalSold || '0')}
                  />
                </StyledBodyTableCell>
                <StyledBodyTableCell
                  key="total"
                  component="th"
                  scope="row"
                  $hasborder
                  colSpan={3}
                  sx={{
                    '&.MuiTableCell-root': {
                      tableLayout: 'auto',
                      borderBottom: 'none',
                      textAlign: 'center',
                      padding: '8px',
                    },
                  }}
                >
                  <Price
                    typographyProps={{ variant: 'heading-xsmall-700', textTransform: 'uppercase' }}
                    showSymbol={false}
                    amount={Number(SerialNumberTrackingItemReport?.totalUsed || '0')}
                  />
                </StyledBodyTableCell>
              </CustomTableRow>
            </Fragment>
          }
        />
      </Box>
    </StyledContainer>
  );
};
