'use client';

import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { useSession } from 'next-auth/react';
import { Fragment, useState, useRef } from 'react';
import { useReactToPrint } from 'react-to-print';
import { Box } from '@mui/material';
import CustomTableRow from '@/components/Atoms/CustomTableRow';
import { TColumnTableReport } from '@/components/Atoms/ReportTable';
import { StyledBodyTableCell } from '@/components/Atoms/ReportTable/ReportTable.styled';
import { Typography } from '@/components/Atoms/Typography/Typography';
import Price from '@/components/Molecules/Price';
import { ReportFilter } from '@/components/Molecules/ReportFilter/ReportFilter';
import ReportHeader from '@/components/Molecules/ReportHeader';
import ServiceReportTable from '@/components/Molecules/ServiceReportTable';
import { FORMAT_TIME_FULL } from '@/lib/constants/dateTime';
import { SWRKey } from '@/lib/constants/SWRKey';
import { useReport } from '@/lib/hooks/queries/report';
import { TRevenueConversionReport } from '@/lib/types/entities/report';
import { ERangeDate } from '@/lib/types/enum/report';
import { toQueryString } from '@/lib/utils/string';
import { customTypography } from '@/theme/customTypography';
import { StyledContainer } from '../Report.styled';

dayjs.extend(utc);
dayjs.extend(timezone);

type TActions = 'edit' | 'delete' | 'create' | 'edit';

type TTableActions = 'edit' | 'delete';

type TFormValues = {
  keySearch?: string;
  startDate?: string;
  endDate?: string;
};

type TReturnItemProps = {
  canExport?: boolean;
};

export const RevenueItem: React.FC<TReturnItemProps> = ({ canExport = false }) => {
  const [filterValues, setFilterValues] = useState<TFormValues>({
    startDate: dayjs().startOf('month').format(FORMAT_TIME_FULL),
    endDate: dayjs().endOf('month').format(FORMAT_TIME_FULL),
  });
  const [pagination, setPagination] = useState(1);
  const componentToPrintRef = useRef<HTMLDivElement>();
  const handlePrint = useReactToPrint({
    content: () => componentToPrintRef?.current || null,
  });
  const { data: revenueItemReport } = useReport<TRevenueConversionReport, TFormValues>(
    'report-revenue-conversion',
    filterValues,
    {
      page: pagination,
      limit: SWRKey.REPORT.LIMIT,
    }
  );
  const { data: session } = useSession();
  const currencyCode = session?.user?.branchChosen?.currency?.code
    ? ` (${session?.user?.branchChosen?.currency?.code})`
    : '';
  const maxPage = revenueItemReport?.pageCount || 1;
  const COLUMNS: TColumnTableReport<TRevenueConversionReport>[] = [
    {
      name: 'date',
      label: 'DATE',
      tableCellProps: { align: 'left' },
      render: (row: TRevenueConversionReport) => <Typography variant="body-medium-400">{row?.date}</Typography>,
    },
    {
      name: 'totalSale',
      label: 'Total sale \n (a)',
      tableCellProps: { align: 'right' },
      render: (row: TRevenueConversionReport) => <Typography variant="body-medium-400">{row?.totalSale}</Typography>,
    },
    {
      name: 'prepaidSale',
      label: 'prepaid sale \n (B)',
      tableCellProps: { align: 'right' },
      render: (row: TRevenueConversionReport) => <Typography variant="body-medium-400">{row?.prepaidSale}</Typography>,
    },
    {
      name: 'otherSale',
      label: 'other sale \n (a) - (B)',
      tableCellProps: { align: 'right' },
      render: (row: TRevenueConversionReport) => <Typography variant="body-medium-400">{row?.otherSale}</Typography>,
    },
    {
      name: 'prepaidConsumption',
      label: 'Prepaid consumption \n (C)',
      tableCellProps: { align: 'right' },
      render: (row: TRevenueConversionReport) => (
        <Typography variant="body-medium-400">{row?.prepaidConsumption}</Typography>
      ),
    },
    {
      name: 'expiredPrepaid',
      label: 'expired prepaid \n (E)',
      tableCellProps: { align: 'right' },
      render: (row: TRevenueConversionReport) => (
        <Typography variant="body-medium-400">{row?.expiredPrepaid}</Typography>
      ),
    },
    {
      name: 'discount',
      label: 'discount \n (D)',
      tableCellProps: { align: 'right' },
      render: (row: TRevenueConversionReport) => <Typography variant="body-medium-400">{row?.discount}</Typography>,
    },
    {
      name: 'netRevenue',
      label: `net revenue \n (A) - (B) + (C) + (D) - (E)`,
      tableCellProps: { align: 'right' },
      render: (row: TRevenueConversionReport) => <Typography variant="body-medium-400">{row?.netRevenue}</Typography>,
    },
  ];

  return (
    <StyledContainer>
      <ReportFilter<TFormValues>
        onFilter={(value: TFormValues) => {
          setPagination(1);
          setFilterValues(value);
        }}
        fieldOptionNames={['keySearch', 'rangeDate']}
        useFormProps={{
          defaultValues: {
            startDate: dayjs().startOf('month').format(FORMAT_TIME_FULL),
            endDate: dayjs().endOf('month').format(FORMAT_TIME_FULL),
            rangeDateType: ERangeDate['SELECT_DATE'],
          } as unknown as TFormValues,
        }}
      />
      <ReportHeader
        title="REVENUE CONVERSION"
        url={`${SWRKey.REPORT.exportByName('report-returned-item')}&fileName=returned-item-report.csv&${toQueryString({
          ...filterValues,
        })}`}
        canExport={canExport}
        // canPrint
        // handlePrint={handlePrint}
      />

      <Box ref={componentToPrintRef}>
        <ServiceReportTable<TRevenueConversionReport>
          page={pagination}
          onLoadMore={() => {
            setPagination(pre => {
              const newPage = pre + 1;
              if (newPage > maxPage) return pre;
              return newPage;
            });
          }}
          rows={revenueItemReport?.data || []}
          columns={COLUMNS}
          showSTT={false}
          tableFooter={
            <Fragment key="table-rows-container-total">
              <CustomTableRow key="table-rows-total">
                <StyledBodyTableCell
                  key="text-total"
                  component="th"
                  scope="row"
                  colSpan={1}
                  sx={{
                    '&.MuiTableCell-root': {
                      tableLayout: 'auto',
                      borderBottom: 'none',
                      textAlign: 'right',
                      padding: '8px',
                    },
                    '& > h5': {
                      fontSize: customTypography['heading-medium-700'],
                      textTransform: 'uppercase',
                    },
                  }}
                >
                  <Typography variant="heading-xsmall-700" textTransform="uppercase">
                    Grand total:
                  </Typography>
                </StyledBodyTableCell>
                <StyledBodyTableCell
                  key="total"
                  component="th"
                  scope="row"
                  $hasborder
                  sx={{
                    '&.MuiTableCell-root': {
                      tableLayout: 'auto',
                      borderBottom: 'none',
                      textAlign: 'right',
                      padding: '8px',
                    },
                  }}
                >
                  <Price
                    typographyProps={{ variant: 'heading-xsmall-700', textTransform: 'uppercase' }}
                    showSymbol={false}
                    amount={Number(revenueItemReport?.sumTotalSale || '0')}
                  />
                </StyledBodyTableCell>
                <StyledBodyTableCell
                  key="total"
                  component="th"
                  scope="row"
                  $hasborder
                  sx={{
                    '&.MuiTableCell-root': {
                      tableLayout: 'auto',
                      borderBottom: 'none',
                      textAlign: 'right',
                      padding: '8px',
                    },
                  }}
                >
                  <Price
                    typographyProps={{ variant: 'heading-xsmall-700', textTransform: 'uppercase' }}
                    showSymbol={false}
                    amount={Number(revenueItemReport?.sumPrepaidSale || '0')}
                  />
                </StyledBodyTableCell>
                <StyledBodyTableCell
                  key="total"
                  component="th"
                  scope="row"
                  $hasborder
                  sx={{
                    '&.MuiTableCell-root': {
                      tableLayout: 'auto',
                      borderBottom: 'none',
                      textAlign: 'right',
                      padding: '8px',
                    },
                  }}
                >
                  <Price
                    typographyProps={{ variant: 'heading-xsmall-700', textTransform: 'uppercase' }}
                    showSymbol={false}
                    amount={Number(revenueItemReport?.sumOtherSale || '0')}
                  />
                </StyledBodyTableCell>
                <StyledBodyTableCell
                  key="total"
                  component="th"
                  scope="row"
                  $hasborder
                  sx={{
                    '&.MuiTableCell-root': {
                      tableLayout: 'auto',
                      borderBottom: 'none',
                      textAlign: 'right',
                      padding: '8px',
                    },
                  }}
                >
                  <Price
                    typographyProps={{ variant: 'heading-xsmall-700', textTransform: 'uppercase' }}
                    showSymbol={false}
                    amount={Number(revenueItemReport?.sumPrepaidConsumption || '0')}
                  />
                </StyledBodyTableCell>
                <StyledBodyTableCell
                  key="total"
                  component="th"
                  scope="row"
                  $hasborder
                  sx={{
                    '&.MuiTableCell-root': {
                      tableLayout: 'auto',
                      borderBottom: 'none',
                      textAlign: 'right',
                      padding: '8px',
                    },
                  }}
                >
                  <Price
                    typographyProps={{ variant: 'heading-xsmall-700', textTransform: 'uppercase' }}
                    showSymbol={false}
                    amount={Number(revenueItemReport?.sumExpiredPrepaid || '0')}
                  />
                </StyledBodyTableCell>
                <StyledBodyTableCell
                  key="total"
                  component="th"
                  scope="row"
                  $hasborder
                  sx={{
                    '&.MuiTableCell-root': {
                      tableLayout: 'auto',
                      borderBottom: 'none',
                      textAlign: 'right',
                      padding: '8px',
                    },
                  }}
                >
                  <Price
                    typographyProps={{ variant: 'heading-xsmall-700', textTransform: 'uppercase' }}
                    showSymbol={false}
                    amount={Number(revenueItemReport?.sumDiscount || '0')}
                  />
                </StyledBodyTableCell>
                <StyledBodyTableCell
                  key="total"
                  component="th"
                  scope="row"
                  $hasborder
                  sx={{
                    '&.MuiTableCell-root': {
                      tableLayout: 'auto',
                      borderBottom: 'none',
                      textAlign: 'right',
                      padding: '8px',
                    },
                  }}
                >
                  <Price
                    typographyProps={{ variant: 'heading-xsmall-700', textTransform: 'uppercase' }}
                    showSymbol={false}
                    amount={Number(revenueItemReport?.sumNetRevenue || '0')}
                  />
                </StyledBodyTableCell>
              </CustomTableRow>
            </Fragment>
          }
        />
      </Box>
    </StyledContainer>
  );
};
