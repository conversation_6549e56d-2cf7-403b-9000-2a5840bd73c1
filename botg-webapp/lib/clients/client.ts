import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { onSeverSession } from '../data/auth';

// Extend dayjs with timezone support
dayjs.extend(utc);
dayjs.extend(timezone);

interface IClient {
  url: string;
  token?: string;
  isClientSide?: boolean;
  header?: HeadersInit;
}

export class Client {
  private baseUrl: string = process.env.API_URL;

  private endPoint = '';

  private header: HeadersInit;

  private isClientSide?: boolean;

  private isServerSide: boolean;

  constructor({ url, token, isClientSide, header }: IClient) {
    const [, urlEndPoint] = url.split('/api/');
    this.endPoint = isClientSide ? url : `${this.baseUrl}/${urlEndPoint}`;
    this.isServerSide = !isClientSide;
    this.isClientSide = isClientSide;

    // Get client timezone for frontend requests
    const clientTimezone = this.isClientSide ? dayjs.tz.guess() : null;
    const timezoneOffset = this.isClientSide ? dayjs().format('Z') : null;

    this.header = {
      'Content-Type': 'application/json',
      ...(token && !isClientSide ? { Authorization: `Bearer ${token}` } : {}),
      ...(clientTimezone && this.isClientSide ? { timezone: clientTimezone } : {}),
      ...(timezoneOffset && this.isClientSide ? { 'timezone-offset': timezoneOffset } : {}),
      ...header,
    };
  }

  async GET() {
    if (this.isServerSide) {
      let headerBranchId = (this.header as Record<string, string>)?.['branchId'];
      if (!headerBranchId) {
        const currentSession = await onSeverSession();
        headerBranchId = currentSession?.user?.branchChosen?.id || '';
      }
      return fetch(this.endPoint, { headers: { ...this.header, branchId: headerBranchId } });
    }
    return fetch(this.endPoint, { headers: this.header });
  }

  async DETAIL(id: string) {
    if (this.isServerSide) {
      const currentSession = await onSeverSession();
      const branchId = currentSession?.user?.branchChosen?.id || '';
      return fetch(this.endPoint, { headers: { ...this.header, branchId } });
    }
    return fetch(`${this.endPoint}/${id}`, { headers: this.header });
  }

  async POST(payload: ReadableStream<Uint8Array> | null | string | FormData) {
    if (this.isServerSide) {
      const currentSession = await onSeverSession();
      const branchId = currentSession?.user?.branchChosen?.id || '';
      return fetch(this.endPoint, { method: 'POST', body: payload, headers: { ...this.header, branchId } });
    }
    return fetch(this.endPoint, { method: 'POST', body: payload, headers: this.header });
  }

  async PATCH(payload: ReadableStream<Uint8Array> | null | string) {
    if (this.isServerSide) {
      const currentSession = await onSeverSession();
      const branchId = currentSession?.user?.branchChosen?.id || '';
      return fetch(this.endPoint, { method: 'PATCH', body: payload, headers: { ...this.header, branchId } });
    }
    return fetch(this.endPoint, { method: 'PATCH', body: payload, headers: this.header });
  }

  async DELETE() {
    if (this.isServerSide) {
      const currentSession = await onSeverSession();
      const branchId = currentSession?.user?.branchChosen?.id || '';
      return fetch(this.endPoint, { method: 'DELETE', headers: { ...this.header, branchId } });
    }
    return fetch(this.endPoint, { method: 'DELETE', headers: this.header });
  }
}
